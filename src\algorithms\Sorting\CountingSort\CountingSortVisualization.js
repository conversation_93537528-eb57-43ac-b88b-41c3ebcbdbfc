// CountingSortVisualization.js - Clean CountingSort visualization following modern pattern
// Uses CountingSortSimulation component and controller-driven architecture

import React, { useEffect, useRef, useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import { useTheme } from '@mui/material/styles';
import { SortingBase, FixedStepBoard, FixedColorLegend } from '../../../components/visualization';
import CountingSortSimulation from '../../../components/visualization/bars/CountingSortSimulation';
import getAlgorithmColors from '../../../utils/algorithmColors';
import { useSpeed } from '../../../context/SpeedContext';
import CONFIG from './CountingSortConfig';
import { getEnhancedDelay } from '../../../utils/speedUtils';

const CountingSortVisualization = ({ array, state, step, totalSteps, setStep, setState, steps }) => {
  const { camera } = useThree();
  const { speed } = useSpeed();
  const theme = useTheme();
  const speedRef = useRef(speed);
  const stateRef = useRef(state);
  const stepRef = useRef(step);

  // Update refs when values change
  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  useEffect(() => {
    stepRef.current = step;
  }, [step]);

  // Get current step data
  const currentStep = useMemo(() => {
    if (!steps || steps.length === 0 || step < 0 || step >= steps.length) {
      return null;
    }
    return steps[step];
  }, [steps, step]);

  // Get algorithm colors
  const colors = useMemo(() => {
    return getAlgorithmColors(theme);
  }, [theme]);

  // Calculate adaptive spacing and scaling
  const { adaptiveSpacing, scaleFactor } = useMemo(() => {
    if (!array || array.length === 0) {
      return { adaptiveSpacing: CONFIG.mainArray.bars.spacing, scaleFactor: 1 };
    }

    const arrayLength = array.length;
    const baseSpacing = CONFIG.mainArray.bars.spacing;
    const maxWidth = 12; // Maximum total width
    const barWidth = CONFIG.mainArray.bars.width;

    // Calculate required width
    const totalBarWidth = arrayLength * barWidth;
    const totalSpacingWidth = (arrayLength - 1) * baseSpacing;
    const totalWidth = totalBarWidth + totalSpacingWidth;

    if (totalWidth > maxWidth) {
      // Scale down to fit
      const scale = maxWidth / totalWidth;
      return {
        adaptiveSpacing: baseSpacing * scale,
        scaleFactor: scale
      };
    }

    return { adaptiveSpacing: baseSpacing, scaleFactor: 1 };
  }, [array]);

  // Auto-advance logic for running state
  useEffect(() => {
    if (state !== 'running' || step >= totalSteps) {
      return;
    }

    console.log('Auto-advance conditions met - should advance to next step soon');

    // Calculate delay based on speed using enhanced delay function
    const speedBasedDelay = getEnhancedDelay(speedRef.current);

    console.log(`Scheduling next step with enhanced speed-based delay: ${speedBasedDelay}ms (speed: ${speedRef.current})`);

    const timeoutId = setTimeout(() => {
      console.log('Auto-advancing to next step with enhanced speed-based timing');
      // Use a direct call to setStep with the next step value
      setStep(step + 1);
    }, speedBasedDelay);

    // Cleanup function
    return () => {
      clearTimeout(timeoutId);
    };
  }, [state, step, totalSteps, setStep, speedRef, steps]);

  // Handle completion
  useEffect(() => {
    if (state === 'running' && step >= totalSteps) {
      console.log('CountingSortVisualization - Algorithm completed, setting state to completed');
      setState('completed');
    }
  }, [state, step, totalSteps, setState]);

  // Camera positioning
  useEffect(() => {
    if (camera) {
      camera.position.set(0, 5, 12);
      camera.lookAt(0, 0, 0);
    }
  }, [camera]);

  // Early return if no data
  if (!array || array.length === 0) {
    return null;
  }

  return (
    <SortingBase>
      {/* Step Board - positioned at the top center */}
      <FixedStepBoard
        currentStep={currentStep}
        position={CONFIG.visual.stepBoard.position}
        width={CONFIG.visual.stepBoard.width}
        height={CONFIG.visual.stepBoard.height}
        config={CONFIG.visual.stepBoard}
      />

      {/* Color Legend - positioned below the visualization */}
      <FixedColorLegend
        colors={colors}
        position={CONFIG.visual.legend.position}
        config={CONFIG.visual.legend}
        items={[
          { key: 'default', label: 'Input Array' },
          { key: 'current', label: 'Current Element' },
          { key: 'counting', label: 'Counting' },
          { key: 'countBar', label: 'Count Array' },
          { key: 'outputBar', label: 'Output Array' },
          { key: 'sorted', label: 'Sorted' },
        ]}
      />

      {/* Bars and labels - positioned relative to the base platform using config offset */}
      <group position={CONFIG.mainArray.bars.baseOffset}>
        <CountingSortSimulation
          currentStep={currentStep}
          colors={colors}
          maxBarHeight={CONFIG.mainArray.bars.maxHeight}
          barWidth={CONFIG.mainArray.bars.width * scaleFactor}
          barSpacing={adaptiveSpacing}
          showValues={(() => {
            const shouldShow = CONFIG.visual.labels.values.adaptiveVisibility
              ? (scaleFactor > CONFIG.visual.labels.values.visibilityThreshold)
              : CONFIG.visual.labels.values.enabled;
            return shouldShow;
          })()}
          showIndices={CONFIG.visual.labels.indices.adaptiveVisibility
            ? (scaleFactor > CONFIG.visual.labels.indices.visibilityThreshold)
            : CONFIG.visual.labels.indices.enabled}
          config={CONFIG}
          state={state}
        />
      </group>
    </SortingBase>
  );
};

export default CountingSortVisualization;
