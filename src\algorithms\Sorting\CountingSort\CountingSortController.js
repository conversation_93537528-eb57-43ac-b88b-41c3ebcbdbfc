// CountingSortController.js - Controller-driven CountingSort following modern pattern
// Generates visualizationData for each step to drive the visualization

import React, { useEffect, useState, useMemo } from 'react';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useSpeed } from '../../../context/SpeedContext';
import { Box, Typography } from '@mui/material';

// Import icons
import ViewArrayIcon from '@mui/icons-material/ViewArray';
import ShuffleIcon from '@mui/icons-material/Shuffle';
import FormatListNumberedIcon from '@mui/icons-material/FormatListNumbered';

// Import common components
import { ProgressSection, ControlsSection, ParametersSection, InformationSection, StepsSequenceSection, AlgorithmSection } from '../../../components/common';

// Import CountingSort algorithm
import { generateCountingSortDetailedSteps } from './CountingSortDetailedSteps';
import CONFIG from './CountingSortConfig';

// Validation utilities
const validateArrayValue = (value) => {
  const isValid = typeof value === 'number' && 
                 !isNaN(value) && 
                 value >= CONFIG.algorithm.array.generation.valueRange.min && 
                 value <= CONFIG.algorithm.array.generation.valueRange.max && 
                 Number.isInteger(value);
  if (!isValid) {
    console.error('Invalid array value:', value);
  }
  return isValid;
};

const validateArray = (array) => {
  return Array.isArray(array) && 
         array.length > 0 && 
         array.length <= CONFIG.algorithm.array.generation.maxSize && 
         array.every(validateArrayValue);
};

const CountingSortController = (props) => {
    // Destructure props
    const { params = {}, onParamChange = () => { } } = props;

    // Get algorithm state from context
    const {
        state,
        setState,
        step,
        setStep,
        totalSteps,
        setTotalSteps,
        steps,
        setSteps,
        setAlgorithmArray
    } = useAlgorithm();

    // Get speed from context
    const { speed, setSpeed } = useSpeed();

    // Extract parameters with safety checks using config defaults
    const arraySize = params?.arraySize || CONFIG.algorithm.array.generation.defaultSize;
    const randomize = params?.randomize !== undefined ? params.randomize : true;
    const customArray = params?.customArray || [];

    // State for custom array input
    const [customArrayInput, setCustomArrayInput] = useState('');
    const [customArrayError, setCustomArrayError] = useState('');
    const [useCustomArray, setUseCustomArray] = useState(false);

    // Debounce mechanism for array size changes
    const [arraySizeTimeoutId, setArraySizeTimeoutId] = useState(null);

    // Generate input array based on parameters with validation
    const inputArray = useMemo(() => {
        try {
            if (customArray && customArray.length > 0) {
                if (!validateArray(customArray)) {
                    throw new Error('Invalid custom array');
                }
                return [...customArray];
            }

            const { min, max } = CONFIG.algorithm.array.generation.valueRange;
            const size = Math.min(arraySize, CONFIG.algorithm.array.generation.maxSize);
            
            const array = Array.from({ length: size }, () => {
                if (randomize) {
                    return Math.floor(Math.random() * (max - min + 1)) + min;
                } else {
                    return min + Math.floor((max - min) * (size / CONFIG.algorithm.array.generation.maxSize));
                }
            });

            if (!validateArray(array)) {
                throw new Error('Generated array validation failed');
            }

            return array;
        } catch (error) {
            console.error('Error generating input array:', error);
            // Return a safe fallback array
            return Array.from({ length: Math.min(arraySize, 10) }, () => 1);
        }
    }, [arraySize, randomize, customArray]);

    // Generate steps when input array changes
    useEffect(() => {
        if (!validateArray(inputArray)) {
            console.error('CountingSortController - Invalid input array');
            return;
        }

        console.log('CountingSortController - Generating steps for array:', inputArray);

        try {
            // Set the validated array in context
            setAlgorithmArray(inputArray);

            // Generate and validate steps
            const generatedSteps = generateCountingSortDetailedSteps(inputArray);
            
            if (!generatedSteps || generatedSteps.length === 0) {
                throw new Error('No steps generated');
            }

            console.log('CountingSortController - Generated steps:', generatedSteps.length);

            // Update visualization state
            setTotalSteps(generatedSteps.length);
            setSteps(generatedSteps);
            setState('idle');
            setStep(0);

            console.log('CountingSortController - Total steps:', generatedSteps.length);
        } catch (error) {
            console.error('CountingSortController - Error:', error);
            setSteps([]);
            setTotalSteps(0);
            setState('idle');
            setStep(0);
        }
    }, [inputArray, setAlgorithmArray, setSteps, setTotalSteps, setState, setStep]);

    // Initialize custom array input when params change
    useEffect(() => {
        if (customArray && customArray.length > 0) {
            setCustomArrayInput(customArray.join(', '));
            setUseCustomArray(true);
        } else {
            setUseCustomArray(false);
        }
    }, [customArray]);

    // Set state to completed when step reaches totalSteps
    useEffect(() => {
        // Only update if we have valid steps and we're not in idle state
        if (totalSteps > 0 && state !== 'idle') {
            // If we've reached the last step, mark as completed
            if (step >= totalSteps) {
                setState('completed');
            }
            // If we were in completed state but stepped back, go to paused
            else if (state === 'completed' && step < totalSteps) {
                setState('paused');
            }
        }
    }, [step, totalSteps, setState, state]);

    // Handle array size change
    const handleArraySizeChange = (newSize) => {
        // Clear any existing timeout
        if (arraySizeTimeoutId) {
            clearTimeout(arraySizeTimeoutId);
        }

        // Set a new timeout to debounce the change
        const timeoutId = setTimeout(() => {
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    arraySize: newSize,
                    customArray: []
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
        }, 300);

        setArraySizeTimeoutId(timeoutId);
    };

    // Handle randomize toggle change
    const handleRandomizeChange = (checked) => {
        if (typeof onParamChange === 'function') {
            onParamChange({
                ...params,
                randomize: checked,
                customArray: []
            });
        }

        // Reset state
        setState('idle');
        setStep(0);
    };

    // Handle custom array toggle change
    const handleUseCustomArrayChange = (checked) => {
        setUseCustomArray(checked);

        if (!checked) {
            // If turning off custom array, revert to random array
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: [],
                    randomize: true
                });
            }
        } else {
            // If turning on custom array
            if (customArrayInput.trim() !== '') {
                // Try to parse the current input if it's not empty
                handleCustomArrayApply();
            } else {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
            }
        }
    };

    // Handle custom array input change
    const handleCustomArrayInputChange = (value) => {
        setCustomArrayInput(value);
    };

    // Handle custom array apply button
    const handleCustomArrayApply = () => {
        try {
            // Check if input is empty
            if (!customArrayInput || customArrayInput.trim() === '') {
                // Provide a default array if input is empty
                const defaultArray = [5, 2, 9, 1, 5, 6];
                setCustomArrayInput(defaultArray.join(', '));
                if (typeof onParamChange === 'function') {
                    onParamChange({
                        ...params,
                        customArray: defaultArray,
                        randomize: false
                    });
                }
                setCustomArrayError('');
                return;
            }

            // Parse the input string into an array of numbers
            const parsedArray = customArrayInput
                .split(',')
                .map(item => item.trim())
                .filter(item => item !== '')
                .map(item => {
                    const num = parseInt(item, 10);
                    if (isNaN(num)) {
                        throw new Error(`Invalid number: ${item}`);
                    }
                    if (num < CONFIG.algorithm.array.generation.valueRange.min) {
                        throw new Error(`Number must be at least ${CONFIG.algorithm.array.generation.valueRange.min}: ${item}`);
                    }
                    if (num > CONFIG.algorithm.array.generation.valueRange.max) {
                        throw new Error(`For better visualization, please use numbers between ${CONFIG.algorithm.array.generation.valueRange.min}-${CONFIG.algorithm.array.generation.valueRange.max}: ${item}`);
                    }
                    return num;
                });

            // Validate array length
            if (parsedArray.length < CONFIG.algorithm.array.generation.minSize) {
                setCustomArrayError(`Please provide at least ${CONFIG.algorithm.array.generation.minSize} numbers`);
                return;
            }

            if (parsedArray.length > CONFIG.algorithm.array.generation.maxSize) {
                setCustomArrayError(`Please provide at most ${CONFIG.algorithm.array.generation.maxSize} numbers`);
                return;
            }

            // Update params
            if (typeof onParamChange === 'function') {
                onParamChange({
                    ...params,
                    customArray: parsedArray,
                    randomize: false
                });
            }

            // Reset state
            setState('idle');
            setStep(0);
            setCustomArrayError('');
        } catch (error) {
            setCustomArrayError(error.message);
        }
    };

    return (
        <Box sx={{ p: 1, height: '100%', overflowY: 'auto' }}>
            {/* Algorithm Title */}
            <Typography variant="h5" gutterBottom>
                Counting Sort
            </Typography>

            {/* Information Section */}
            <InformationSection defaultExpanded={false}>
                <Box>
                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        About Counting Sort:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        Counting Sort is a non-comparative sorting algorithm that works well when the range of input values is not significantly larger than the number of elements to be sorted. It counts the occurrences of each element and uses this information to place elements in their correct positions.
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Time Complexity:
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Best Case: O(n+k) where n is the number of elements and k is the range of input
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 0.5 }}>
                        - Average Case: O(n+k)
                    </Typography>
                    <Typography variant="body2" sx={{ mb: 1 }}>
                        - Worst Case: O(n+k)
                    </Typography>

                    <Typography variant="body2" sx={{ mb: 0.5, fontWeight: 500 }}>
                        Space Complexity:
                    </Typography>
                    <Typography variant="body2">
                        O(n+k) where n is the number of elements and k is the range of input
                    </Typography>
                </Box>
            </InformationSection>

            {/* Parameters Section */}
            <ParametersSection
                parameters={[
                    {
                        name: 'arraySize',
                        type: 'slider',
                        label: 'Array Size',
                        min: CONFIG.algorithm.array.generation.minSize,
                        max: CONFIG.algorithm.array.generation.maxSize,
                        step: 1,
                        defaultValue: arraySize,
                        icon: ViewArrayIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'randomize',
                        type: 'switch',
                        label: 'Randomize Array',
                        defaultValue: randomize,
                        icon: ShuffleIcon,
                        disabled: useCustomArray
                    },
                    {
                        name: 'useCustomArray',
                        type: 'switch',
                        label: 'Use Custom Array',
                        defaultValue: useCustomArray,
                        icon: FormatListNumberedIcon
                    },
                    {
                        name: 'customArrayInput',
                        type: 'customArray',
                        label: 'Custom Array',
                        showOnlyWhen: 'useCustomArray',
                        error: customArrayError,
                        helperText: `Enter comma-separated numbers between ${CONFIG.algorithm.array.generation.valueRange.min}-${CONFIG.algorithm.array.generation.valueRange.max} (e.g., 5, 3, 8, 1). Maximum ${CONFIG.algorithm.array.generation.maxSize} numbers allowed.`,
                        placeholder: `e.g., 5, 3, 8, 4, 2 (min ${CONFIG.algorithm.array.generation.minSize}, max ${CONFIG.algorithm.array.generation.maxSize} numbers)`,
                        onApply: handleCustomArrayApply,
                        icon: FormatListNumberedIcon
                    }
                ]}
                values={{
                    arraySize,
                    randomize,
                    useCustomArray,
                    customArrayInput
                }}
                onChange={(newValues) => {
                    // Handle parameter changes
                    if (newValues.arraySize !== undefined && newValues.arraySize !== arraySize) {
                        handleArraySizeChange(newValues.arraySize);
                    }

                    if (newValues.randomize !== undefined && newValues.randomize !== randomize) {
                        handleRandomizeChange(newValues.randomize);
                    }

                    if (newValues.useCustomArray !== undefined && newValues.useCustomArray !== useCustomArray) {
                        handleUseCustomArrayChange(newValues.useCustomArray);
                    }

                    if (newValues.customArrayInput !== undefined && newValues.customArrayInput !== customArrayInput) {
                        handleCustomArrayInputChange(newValues.customArrayInput);
                    }
                }}
                disabled={state === 'running'}
            />

            {/* Controls Section */}
            <ControlsSection
                state={state}
                step={step}
                totalSteps={totalSteps}
                speed={speed}
                setSpeed={setSpeed}
                onStart={() => setState('running')}
                onPause={() => setState('paused')}
                onReset={() => {
                    // First set step to 0, then set state to idle
                    setStep(0);
                    setTimeout(() => {
                        setState('idle');
                    }, 50); // Small delay to ensure step is reset first
                }}
                onStepForward={() => {
                    if (step < totalSteps) {
                        setStep(step + 1);
                        // If this will be the last step, mark as completed
                        if (step + 1 >= totalSteps) {
                            setState('completed');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                onStepBackward={() => {
                    if (step > 0) {
                        setStep(step - 1);
                        // If we were in completed state, go back to paused
                        if (state === 'completed') {
                            setState('paused');
                        }
                        // If we were in idle state, go to paused
                        else if (state === 'idle') {
                            setState('paused');
                        }
                    }
                }}
                showStepControls={true}
            />

            {/* Progress Indicator Section */}
            <ProgressSection
                state={state}
                step={step}
                totalSteps={totalSteps}
            />

            {/* Steps Sequence Section */}
            <StepsSequenceSection
                steps={(steps || []).map(step => ({
                    description: step.statement || step.movement || ''
                }))}
                currentStep={step}
                defaultExpanded
                emptyMessage="No steps yet. Start the algorithm to see the sequence."
            />

            {/* Algorithm Section */}
            <AlgorithmSection
                title="Counting Sort Algorithm"
                defaultExpanded
                currentStep={step >= 0 && steps && steps.length > 0 ?
                    // Map the detailed step types to corresponding line numbers
                    steps[step]?.type === 'initial' ? 0 :
                    steps[step]?.type === 'findMax' ? 1 :
                    steps[step]?.type === 'initCount' ? 2 :
                    steps[step]?.type === 'counting' || steps[step]?.type === 'incrementCount' ? 3 :
                    steps[step]?.type === 'calculatePosition' || steps[step]?.type === 'updatePosition' ? 5 :
                    steps[step]?.type === 'initOutput' ? 7 :
                    steps[step]?.type === 'placing' || steps[step]?.type === 'placed' || steps[step]?.type === 'decrementCount' ? 8 :
                    steps[step]?.type === 'complete' ? 13 : 0
                    : 0
                }
                algorithm={[
                    { code: "function countingSort(arr):", lineNumber: 0, indent: 0 },
                    { code: "max = findMaximum(arr)", lineNumber: 1, indent: 1 },
                    { code: "count = new Array(max + 1).fill(0)", lineNumber: 2, indent: 1 },
                    { code: "for i = 0 to length(arr) - 1:", lineNumber: 3, indent: 1 },
                    { code: "    count[arr[i]]++", lineNumber: 4, indent: 2 },
                    { code: "for i = 1 to max:", lineNumber: 5, indent: 1 },
                    { code: "    count[i] += count[i - 1]", lineNumber: 6, indent: 2 },
                    { code: "output = new Array(length(arr))", lineNumber: 7, indent: 1 },
                    { code: "for i = length(arr) - 1 to 0:", lineNumber: 8, indent: 1 },
                    { code: "    output[count[arr[i]] - 1] = arr[i]", lineNumber: 9, indent: 2 },
                    { code: "    count[arr[i]]--", lineNumber: 10, indent: 2 },
                    { code: "for i = 0 to length(arr) - 1:", lineNumber: 11, indent: 1 },
                    { code: "    arr[i] = output[i]", lineNumber: 12, indent: 2 },
                    { code: "return arr", lineNumber: 13, indent: 1 },
                ]}
            />
        </Box>
    );
};

export default CountingSortController;
