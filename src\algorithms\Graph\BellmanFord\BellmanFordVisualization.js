// BellmanFordVisualization.js
// This component provides the visualization for the Bellman-Ford algorithm.

import React, { useState, useEffect, useRef, useMemo } from 'react';
import { useThree } from '@react-three/fiber';
import { useSpeed } from '../../../context/SpeedContext';
import { useAlgorithm } from '../../../context/AlgorithmContext';
import { useTheme } from '@mui/material/styles';
import { generateBellmanFordSteps } from './BellmanFordAlgorithm';
import * as THREE from 'three';

// Import reusable visualization components
import { ColorLegend, StepBoard, GroundPlane, GraphNode, GraphEdge } from '../../../components/visualization';

// Constants for visualization
const NODE_RADIUS = 0.8;
const NODE_SEGMENTS = 32;
const EDGE_WIDTH = 0.1;
const CAMERA_POSITION = [0, 15, 20];
const CAMERA_LOOKAT = [0, 0, 0];

// Constants for visualization are now used only for reference

// Main visualization component
const BellmanFordVisualization = () => {
  const theme = useTheme();
  const { camera, scene } = useThree();
  const { state, setState, step, setStep, setAlgorithmSteps, setTotalSteps, setSteps } = useAlgorithm();
  const { speed } = useSpeed();

  // Refs for animation control
  const speedRef = useRef(speed);
  const stepsRef = useRef([]);
  const timeoutIdRef = useRef(null);
  const stateRef = useRef(state);
  const currentStepRef = useRef(step);

  // State for graph data
  const [graphData, setGraphData] = useState({ nodes: [], edges: [] });
  const [startNode, setStartNode] = useState('A');

  // State for visualization
  const [distances, setDistances] = useState({});
  const [currentEdge, setCurrentEdge] = useState(null);
  const [relaxedEdge, setRelaxedEdge] = useState(null);
  const [negCycleEdge, setNegCycleEdge] = useState(null);
  const [negCyclePath, setNegCyclePath] = useState(null);
  const [highlightedNodes, setHighlightedNodes] = useState([]);

  // Get theme-aware colors
  const colors = useMemo(() => {
    const isDark = theme.palette.mode === 'dark';
    return {
      node: isDark ? '#64b5f6' : '#2196f3',
      startNode: isDark ? '#81c784' : '#4caf50',
      edge: isDark ? '#9e9e9e' : '#757575',
      currentEdge: isDark ? '#ffb74d' : '#ff9800',
      relaxedEdge: isDark ? '#81c784' : '#4caf50',
      negCycleEdge: isDark ? '#e57373' : '#f44336',
      negCyclePath: isDark ? '#e57373' : '#f44336',
      unreachableNode: isDark ? '#9e9e9e' : '#757575',
      ground: isDark ? '#121212' : '#f5f5f5',
    };
  }, [theme.palette.mode]);

  // Update refs when state changes
  useEffect(() => {
    stateRef.current = state;
  }, [state]);

  useEffect(() => {
    currentStepRef.current = step;
  }, [step]);

  useEffect(() => {
    speedRef.current = speed;
  }, [speed]);

  // Set scene background color based on theme
  useEffect(() => {
    if (scene) {
      scene.background = new THREE.Color(colors.ground);
    }
  }, [scene, colors.ground]);

  // Apply a step of the algorithm
  const applyStep = (stepIndex) => {
    if (stepIndex < 0 || stepIndex >= stepsRef.current.length) {
      return;
    }

    const currentStep = stepsRef.current[stepIndex];

    // Update visualization state based on step data
    if (currentStep.distances) {
      setDistances(currentStep.distances);
    }

    setCurrentEdge(currentStep.currentEdge || null);
    setRelaxedEdge(currentStep.relaxedEdge || null);
    setNegCycleEdge(currentStep.negCycleEdge || null);
    setNegCyclePath(currentStep.negCyclePath || null);

    // Update highlighted nodes based on step type
    switch (currentStep.type) {
      case 'init':
      case 'initialize':
        setHighlightedNodes([startNode]);
        break;
      case 'considerEdge':
        if (currentStep.currentEdge) {
          setHighlightedNodes([currentStep.currentEdge.source, currentStep.currentEdge.target]);
        }
        break;
      case 'relaxEdge':
        if (currentStep.relaxedEdge) {
          setHighlightedNodes([currentStep.relaxedEdge.target]);
        }
        break;
      case 'negCycleFound':
        if (currentStep.negCyclePath) {
          setHighlightedNodes(currentStep.negCyclePath);
        }
        break;
      default:
        break;
    }
  };

  // Handle step changes
  useEffect(() => {
    // Apply the current step
    applyStep(step);
  }, [step, startNode]);

  // Handle automatic stepping when state is 'running'
  useEffect(() => {
    // Only proceed if state is 'running'
    if (state !== 'running') {
      return;
    }

    const steps = stepsRef.current || [];

    // Stop if we've reached the end
    if (step >= steps.length) {
      if (setState && typeof setState === 'function') {
        setState('completed');
      }
      return;
    }

    // Schedule the next step with a delay
    const delay = Math.max(500, 2000 - (speedRef.current * 200));
    const timeoutId = setTimeout(() => {
      // Only increment if still in running state
      if (stateRef.current === 'running') {
        if (setStep && typeof setStep === 'function') {
          setStep(prevStep => prevStep + 1);
        }
      }
    }, delay);

    // Store the timeout ID for cleanup
    timeoutIdRef.current = timeoutId;

    // Cleanup function
    return () => {
      if (timeoutIdRef.current) {
        clearTimeout(timeoutIdRef.current);
        timeoutIdRef.current = null;
      }
    };
  }, [state, step, setStep, setState]);

  // Initialize graph when params change
  useEffect(() => {
    // This effect will run when the component mounts
    // We'll use a simple default graph for testing
    const defaultGraph = {
      nodes: [
        { id: 'A', label: 'A', x: -5, y: 0 },
        { id: 'B', label: 'B', x: 0, y: 5 },
        { id: 'C', label: 'C', x: 5, y: 0 },
        { id: 'D', label: 'D', x: 0, y: -5 }
      ],
      edges: [
        { id: '1', source: 'A', target: 'B', weight: 1 },
        { id: '2', source: 'B', target: 'C', weight: 2 },
        { id: '3', source: 'C', target: 'D', weight: 3 },
        { id: '4', source: 'D', target: 'A', weight: 4 }
      ]
    };

    const defaultStartNode = 'A';

    // Update graph data
    setGraphData(defaultGraph);
    setStartNode(defaultStartNode);

    // Generate steps
    const { steps } = generateBellmanFordSteps(defaultGraph, defaultStartNode);
    stepsRef.current = steps;

    if (setSteps && typeof setSteps === 'function') {
      setSteps(steps);
    }

    if (setTotalSteps && typeof setTotalSteps === 'function') {
      setTotalSteps(steps.length);
    }

    // Note: setMovements is not available in this context

    // Reset step if needed
    if (setStep && typeof setStep === 'function') {
      setStep(0);
    }

    // Position camera
    if (camera) {
      camera.position.set(...CAMERA_POSITION);
      camera.lookAt(...CAMERA_LOOKAT);
    }
  }, [camera, setStep, setTotalSteps, setSteps, setAlgorithmSteps]);

  // Check if an edge is part of a negative cycle
  const isEdgeInNegativeCycle = (edge) => {
    if (!negCyclePath || negCyclePath.length < 2) return false;

    for (let i = 0; i < negCyclePath.length - 1; i++) {
      if (edge.source === negCyclePath[i] && edge.target === negCyclePath[i + 1]) {
        return true;
      }
    }

    // Check the last edge that completes the cycle
    if (edge.source === negCyclePath[negCyclePath.length - 1] && edge.target === negCyclePath[0]) {
      return true;
    }

    return false;
  };

  // Check if a node is part of a negative cycle
  const isNodeInNegativeCycle = (nodeId) => {
    if (!negCyclePath) return false;
    return negCyclePath.includes(nodeId);
  };

  // Create a function to determine if edges should be curved (for bidirectional edges)
  const getEdgeDirections = () => {
    const directions = {};
    const edges = graphData.edges || [];

    // Identify bidirectional edges
    edges.forEach(edge => {
      const key = `${edge.source}-${edge.target}`;
      const reverseKey = `${edge.target}-${edge.source}`;

      if (!directions[key]) {
        directions[key] = { count: 1, curved: false };
      } else {
        directions[key].count += 1;
      }

      if (!directions[reverseKey]) {
        directions[reverseKey] = { count: 0, curved: false };
      }
    });

    // Mark edges as curved if they are bidirectional
    Object.keys(directions).forEach(key => {
      const [source, target] = key.split('-');
      const reverseKey = `${target}-${source}`;

      if (directions[reverseKey] && directions[reverseKey].count > 0) {
        directions[key].curved = true;
      }
    });

    return directions;
  };

  // Calculate edge directions
  const edgeDirections = useMemo(() => getEdgeDirections(), [graphData.edges]);

  // Handle node click
  const handleNodeClick = (node) => {
    console.log('Node clicked:', node);
  };

  // Handle edge click
  const handleEdgeClick = (edge) => {
    console.log('Edge clicked:', edge);
  };

  return (
    <group>
      {/* Lighting */}
      <ambientLight intensity={0.6} />
      <directionalLight position={[10, 10, 10]} intensity={0.8} castShadow />
      <directionalLight position={[-10, 10, -10]} intensity={0.4} />
      <pointLight position={[0, 8, 0]} intensity={0.3} color="#ffffff" />

      {/* Ground plane */}
      <GroundPlane
        position={[0, -0.5, 0]}
        width={100}
        depth={100}
        color={colors.ground}
        receiveShadow={true}
      />

      {/* Step Board */}
      <StepBoard
        position={[0, 8, 0]}
        description={stepsRef.current[step]?.message || ''}
        stepData={stepsRef.current[step] || {}}
        currentStep={step + 1}
        totalSteps={stepsRef.current.length}
        showBackground={true}
      />

      {/* Color Legend */}
      <ColorLegend
        position={[0, -3, 10]}
        colors={colors}
        items={[
          { label: 'Node', color: colors.node },
          { label: 'Start Node', color: colors.startNode },
          { label: 'Edge', color: colors.edge },
          { label: 'Current Edge', color: colors.currentEdge },
          { label: 'Relaxed Edge', color: colors.relaxedEdge },
          { label: 'Negative Cycle', color: colors.negCycleEdge }
        ]}
      />

      {/* Graph Visualization */}
      <group>
        {/* Render edges */}
        {graphData.edges && graphData.edges.map(edge => {
          const sourceNode = graphData.nodes.find(node => node.id === edge.source);
          const targetNode = graphData.nodes.find(node => node.id === edge.target);

          if (!sourceNode || !targetNode) return null;

          const start = [sourceNode.x, 0, sourceNode.y || 0];
          const end = [targetNode.x, 0, targetNode.y || 0];

          // Determine edge color and highlighting
          const isCurrentEdge = currentEdge && currentEdge.id === edge.id;
          const isRelaxedEdge = relaxedEdge && relaxedEdge.id === edge.id;
          const isNegCycleEdge = negCycleEdge && negCycleEdge.id === edge.id;
          const isInNegCycle = isEdgeInNegativeCycle(edge);

          let edgeColor = colors.edge;
          if (isCurrentEdge) edgeColor = colors.currentEdge;
          if (isRelaxedEdge) edgeColor = colors.relaxedEdge;
          if (isNegCycleEdge || isInNegCycle) edgeColor = colors.negCycleEdge;

          // Check if edge should be curved
          const directionKey = `${edge.source}-${edge.target}`;
          const isCurved = edgeDirections[directionKey]?.curved || false;

          return (
            <GraphEdge
              key={edge.id}
              start={new THREE.Vector3(...start)}
              end={new THREE.Vector3(...end)}
              weight={edge.weight}
              color={edgeColor}
              isHighlighted={isCurrentEdge}
              isRelaxed={isRelaxedEdge}
              isNegativeCycle={isNegCycleEdge || isInNegCycle}
              curved={isCurved}
              curveHeight={1.5}
              onClick={() => handleEdgeClick(edge)}
              nodeRadius={NODE_RADIUS}
              thickness={EDGE_WIDTH}
            />
          );
        })}

        {/* Render nodes */}
        {graphData.nodes && graphData.nodes.map(node => {
          const position = [node.x, 0, node.y || 0];
          const isStart = node.id === startNode;
          const distance = distances[node.id];
          const isInNegCycle = isNodeInNegativeCycle(node.id);
          const isHighlighted = highlightedNodes.includes(node.id) || isInNegCycle;

          // Determine node color
          let nodeColor = colors.node;
          if (isStart) nodeColor = colors.startNode;
          if (distance === Infinity) nodeColor = colors.unreachableNode;
          if (isInNegCycle) nodeColor = colors.negCyclePath;

          return (
            <GraphNode
              key={node.id}
              position={position}
              label={node.label || node.id}
              color={nodeColor}
              isStart={isStart}
              isHighlighted={isHighlighted}
              distance={distance}
              onClick={() => handleNodeClick(node)}
              radius={NODE_RADIUS}
              segments={NODE_SEGMENTS}
            />
          );
        })}
      </group>
    </group>
  );
};

export default BellmanFordVisualization;
