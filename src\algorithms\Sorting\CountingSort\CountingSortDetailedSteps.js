// CountingSortDetailedSteps.js
// Detailed step generation for CountingSort algorithm

/**
 * Generates detailed steps for the CountingSort algorithm
 * @param {Array} inputArray - The array to sort
 * @returns {Array} - Array of detailed steps for visualization
 */
export const generateCountingSortDetailedSteps = (inputArray) => {
  // Create a copy of the array to avoid modifying the original
  const arr = [...inputArray];
  const steps = [];

  // Add initial step
  steps.push({
    type: 'initial',
    statement: `Counting Sort: Initial array [${arr.join(', ')}] - Ready to sort by counting occurrences`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        currentIndex: null,
        currentElement: null,
        sortedIndices: [],
      },
      countArray: [],
      outputArray: [],
      maxValue: null,
    }
  });

  // Find the maximum element
  const max = Math.max(...arr);
  steps.push({
    type: 'findMax',
    statement: `Find maximum value in array: ${max}`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        currentIndex: null,
        currentElement: null,
        sortedIndices: [],
      },
      countArray: [],
      outputArray: [],
      maxValue: max,
    }
  });

  // Initialize count array
  const count = new Array(max + 1).fill(0);
  steps.push({
    type: 'initCount',
    statement: `Initialize count array of size ${max + 1} with zeros`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        currentIndex: null,
        currentElement: null,
        sortedIndices: [],
      },
      countArray: [...count],
      outputArray: [],
      maxValue: max,
    }
  });

  // Count occurrences
  for (let i = 0; i < arr.length; i++) {
    const element = arr[i];

    // Before incrementing count
    steps.push({
      type: 'counting',
      statement: `Count element ${element} at index ${i}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: i,
          currentElement: element,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [],
        maxValue: max,
      }
    });

    // Increment count
    count[element]++;

    // After incrementing count
    steps.push({
      type: 'incrementCount',
      statement: `Incremented count for ${element} to ${count[element]}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: i,
          currentElement: element,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [],
        maxValue: max,
      }
    });
  }

  // Calculate cumulative positions
  for (let i = 1; i <= max; i++) {
    // Before updating position
    steps.push({
      type: 'calculatePosition',
      statement: `Calculate cumulative position for ${i}: ${count[i]} + ${count[i-1]}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: null,
          currentElement: null,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [],
        maxValue: max,
      }
    });

    // Update position
    count[i] += count[i - 1];

    // After updating position
    steps.push({
      type: 'updatePosition',
      statement: `Updated cumulative position for ${i} to ${count[i]}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: null,
          currentElement: null,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [],
        maxValue: max,
      }
    });
  }

  // Create output array
  const output = new Array(arr.length).fill(null);
  steps.push({
    type: 'initOutput',
    statement: `Initialize output array of size ${arr.length}`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        currentIndex: null,
        currentElement: null,
        sortedIndices: [],
      },
      countArray: [...count],
      outputArray: [...output],
      maxValue: max,
    }
  });

  // Place elements in output array (from right to left for stability)
  for (let i = arr.length - 1; i >= 0; i--) {
    const element = arr[i];
    const targetIndex = count[element] - 1;

    // Before placing element
    steps.push({
      type: 'placing',
      statement: `Place element ${element} from index ${i} to position ${targetIndex} in output array`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: i,
          currentElement: element,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [...output],
        maxValue: max,
        targetIndex: targetIndex,
      }
    });

    // Place element
    output[targetIndex] = element;

    // After placing element
    steps.push({
      type: 'placed',
      statement: `Placed element ${element} at position ${targetIndex}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: i,
          currentElement: element,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [...output],
        maxValue: max,
        targetIndex: targetIndex,
      }
    });

    // Decrement count
    count[element]--;

    // Show count decrement
    steps.push({
      type: 'decrementCount',
      statement: `Decremented count for ${element} to ${count[element]}`,
      visualizationData: {
        mainArray: {
          values: [...arr],
          currentIndex: i,
          currentElement: element,
          sortedIndices: [],
        },
        countArray: [...count],
        outputArray: [...output],
        maxValue: max,
      }
    });
  }

  // Copy output back to original array
  for (let i = 0; i < output.length; i++) {
    arr[i] = output[i];
  }

  // Final completion step
  const sortedIndices = Array.from({ length: arr.length }, (_, i) => i);
  steps.push({
    type: 'complete',
    statement: `Counting Sort completed! Final sorted array: [${arr.join(', ')}]`,
    visualizationData: {
      mainArray: {
        values: [...arr],
        currentIndex: null,
        currentElement: null,
        sortedIndices: sortedIndices,
      },
      countArray: [],
      outputArray: [],
      maxValue: null,
    }
  });

  return steps;
};

export default generateCountingSortDetailedSteps;
