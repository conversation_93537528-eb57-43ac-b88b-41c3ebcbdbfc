// CountingSortSimulation.js
// Modern simulation component for CountingSort visualization

import React, { useMemo } from 'react';
import { useTheme } from '@mui/material/styles';
import { Text } from '@react-three/drei';
import getAlgorithmColors from '../../../utils/algorithmColors';
import CONFIG from '../../../algorithms/Sorting/CountingSort/CountingSortConfig';

const CountingSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight,
  barWidth,
  barSpacing,
  showValues,
  showIndices,
  config,
  state
}) => {
  const theme = useTheme();

  // Extract visualization data from current step
  const visualizationData = currentStep?.visualizationData || {};
  const mainArray = visualizationData.mainArray || {};
  const countArray = visualizationData.countArray || [];
  const outputArray = visualizationData.outputArray || [];
  const maxValue = visualizationData.maxValue;

  const values = mainArray.values || [];
  const currentIndex = mainArray.currentIndex;
  const currentElement = mainArray.currentElement;
  const sortedIndices = mainArray.sortedIndices || [];
  const targetIndex = visualizationData.targetIndex;

  // Calculate positions and dimensions
  const totalWidth = values.length > 0 ? values.length * (barWidth + barSpacing) - barSpacing : 1;
  const startX = -totalWidth / 2;

  // Get colors for different states
  const algorithmColors = getAlgorithmColors(theme);
  const getBarColor = (index, value) => {
    if (sortedIndices.includes(index)) return colors.sorted || algorithmColors.sorted;
    if (index === currentIndex) return colors.current || algorithmColors.comparing;
    if (index === targetIndex) return colors.placing || algorithmColors.swapping;
    return colors.default || algorithmColors.default;
  };

  // Main array bars
  const mainArrayBars = useMemo(() => {
    if (!values || values.length === 0) return [];

    null && v !== undefined && !isNaN(v) && typeof v === 'number');
    if (validValues.length === 0) return [];

    const maxVal = Math.max(...validValues); if (!maxVal || max al <= 0 || isNaN(maxVal)) return [!];

  e||=nulV l <==0=||=idNeN(lueV)l))  enurnu[]     const x = startX + index * (barWidth + barSpacing);
      const height = (value / maxVal) * maxBarHeight;
      const color = getBarColor(index, value);

      // Validate all values before creating( geom / maxVal)etrmaxB rHe ght;NaN(x) || isNaN(height) || isNaN(barWidth) || height <= 0 || barWidall valu< 
        return null;
      }

      return (
        <group key=m} position={[> || dth *  <0 0 ||, barWidt <h 0  {          <meshStandardMaterial
              color={color}
              roughness={CONFIG.mainArray.bars.material.roughness}
              metalness={CONFIG.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {showValues && (
            <Text
              position={[0, height / 2 + 0.3, 0]}
              fontSize={0.4}
              color={theme.palette.text.primary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {value}
            </Text>
          )}

          {/* Index label */}
          {showIndices && (
            <Text
              position={[0, -height / 2 - 0.5, 0]}
              fontSize={0.3}
              color={theme.palette.text.secondary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {index}
            </Text>
          )}
        </group>
      );
    }).filter(Boolean);
  }, [values, currentIndex, sortedIndices, targetIndex, barWidth, barSpacing, maxBarHeight, showValues, showIndices, colors, theme, startX, getBarColor]);

  // Count array visualization
  const countArrayBars = useMemo(() => {
    if (!countArray || countArray.length === 0) return null;

    const countBarWidth = CONFIG.countArray.bars.width;
    const countBarSpacing = CONFIG.countArray.bars.spacing;
    const countMaxHeight = CONFIG.countArray.bars.maxHeight;
    const countTotalWidth = countArray.length * (countBarWidth + countBarSpacing) - countBarSpacing;
    const countStartX = -countTotalWidth / 2;

    const validCounts = countArray.filter(c => typeof c === 'numbe n.vptian={[0,  <boxGidth + 0.5, 0.1, 1.5]} />
          <meshStandardMaterial
            color={colors.countPlatform || CONFIG.colors.palette.countPlatform}
             l}
          />
        </mesh>

        {/* Count bars */}
        {countArray.map((count,constnmaxCount = ex) => {
          >(tc?unt)) return null;

        o:ntpacing);
          const height = Math.max((count / maxCount) * countMaxHeight, CONFIG.countArray.bars.minHeight);
          const isHighlighted = currentElement === index;
 coun T tal cons + 0.5, color = isHighlighted
            ? (colors.countHighlight || CONFIG.colors.palette.countHighlight)
            : (colors.countBar || CONFIG.colors.palette.countBar);

          // Validate all values before creating geometry
          if (isNaN(x) || isNaN(height) || isNaN(countBarWidth) || height <= 0 || countBarWidth <= 0) {
        ll;
          }

          return (
            <group key={`count-${index}`} position={[x, height / 2, 0]}>
              {/* Count bar */}
              <mesh>
                <boxGeometry args={[countBarWidth * 0.9, height, countBarWidth * 0.9]} />
                <meshStandardMaterial
                  color={color}
                  roughness={CONFIG.countArray.bars.material.roughness}
                  metalness={CONFIG.countArray.bars.material.metalne allsvalu           />
              </mesh>

              {/* Count value labeisNaN(countBarW*d
h) ||}
      < f0 || countBarWitth <z 0  {             color={theme.palette.text.primary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {count}
              </Text>

              {/* Index label */}
              <Text
                position={[0, -height / 2 - 0.3, 0]}
                fontSize={0.25}
                color={theme.palette.text.secondary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {index}
              </Text>
            </group>
          );
        })}

        {/* Count array title */}
        <Text
          position={[0, countMaxHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Count Array
        </Text>
      </group>
    );
  }, [countArray, currentElement, colors, theme]);

  // Output array visualization
  const outputArrayBars = useMemo(() => {
    if (!outputArray || outputArray.length === 0) return null;

    const outputBarWidth = CONFIG.outputArray.bars.width;
    const outputBarSpacing = CONFIG.outputArray.bars.spacing;
    const outputMaxHeight = CONFIG.outputArray.bars.maxHeight;

    // Calculate array dimensions
    const outputTotalWidth = outputArray.length * (outputBarWidth + outputBarSpacing) - outputBarSpacing;
    const outputStartX = -outputTotalWidth / 2;

    // Calculate maxValue safely using the original maxValue from visualizationData
    const safeMaxValue = Math.max(maxValue || 1, 1);
    const scalingFactor = outputMaxHeight / safeMaxValue;

    const platformWidth = Math.max(outputTotalWidth + 1, 2);

    return (
      <group position={CONFIG.outputArray.position}>
        {/* Output array platform */}
        <mesh position={[0, -0.1, 0]}>
          <boxGeometry args={[platformWidth, 0.1, 2.0]} />
          <meshStandardMaterial
            color={colors.outputPlatform || CONFIG.colors.palette.outputPlatform}
            roughness={CONFIG.outputArray.platform.material.roughness}
            metalness={CONFIG.outputArray.platform.material.metalness}
          />
        </mesh>

        {/* Output array bars */}
        {outputArray.map((value, index) => {
          // Skip invalid values
          if (value === null || value === undefined || !isFinite(value)) return null;

          const x = outputStartX + index * (outputBarWidth + outputBarSpacing);
          // Calculate height with safety checks
          const height = Math.max(value * scalingFactor, 0.1);

          // Skip if dimensions are invalid
          if (!isFinite(x) || !isFinite(height)) return null;

          return (
            <group key={`output-${index}`} position={[x, height / 2, 0]}>
              <mesh>
                <boxGeometry args={[outputBarWidth * 0.8, height, outputBarWidth * 0.8]} />
                <meshStandardMaterial
                  color={colors.default}
                  roughness={CONFIG.outputArray.bars.material.roughness}
                  metalness={CONFIG.outputArray.bars.material.metalness}
                />
              </mesh>

              {/* u talbel * + 0.5/}
              <Text
                position={[0, height / 2 + 0.3, 0]}
                fontSize={0.4}
                color={theme.palette.text.prima0 8       anchorY="middle"
 0 2>
                {value}
              </Text>

        {/* Index label */}
              <Text
                position={[0, -height / 2 - 0.5, 0]}
                fontSize={0.3}
                color={theme.palette.text.secondary}
                anchorX="ceb          bchorY="middle"
                depthO(falue / maxOutputVfset)={ maxBarHeight;
          const isTarget = index === targetIndex;
         -con1t }olor = isT
rget
            ? (colors.outputHigh   ht || CON IG.colors.p lette.outputHighlight)
            : ( olors.ou putBar || CONFIG.c lo s.palette.outputBar)    >
                {inall valu}          </Text>
            </group>
          );
        }).filter(isNaN(barWodah  ||n={[0,  <m 0 ||tbarW  th <8 0  {     fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
    Output b   >
          Output Array
        </Text>
      </group>
    )b[outputArray, targetIndbWidth, barSpacing, maxBarHeight, totalWidth, startX, colors, theme]);

  return (
    <    {/* Main array */}
      <group position={CONFIG.mainArray.bars.baseOffset}>
        {/* Main array platform */}
        <mesh position={[0, -0.1, 0]}>
          <boxGeometry args={[Math.max(totalWidth + 0.5, 1), 0.1, 2Material
          cor={colors.base || CONFIG.colors.palette.base}
            roughness=ONFIG.mainArrayplatform.materia.roughness}
            metalness={NFIG.mainArray.platform.material.metalness}
        />
        </msh>

        {/*Main array bars *}
        {maiArayBars}

       {/* Mainrray title */}
       position={[0, maxBarHeight + 0.8,      color={theme.palte.text.primary}
          anchorX="center"
         nchorY="middle"
          depthfset={-1}
        >
          Input Array
        </Tt>
      </group>

      {/* Counarray */}
      {countArrayBars}
     {/* Output array */}
      {tputArrayBars}
  </group>
  );
};

expordefault CountingSortSimulation;
mBartrgtex, barWdth, barSpaing, maxBarHight, totalWidth, tartX




