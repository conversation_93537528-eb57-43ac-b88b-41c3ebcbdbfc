// CountingSortSimulation.js
// Modern simulation component for CountingSort visualization

import React, { useMemo } from 'react';
import { useTheme } from '@mui/material/styles';
import { Text } from '@react-three/drei';
import getAlgorithmColors from '../../../utils/algorithmColors';
import CONFIG from '../../../algorithms/Sorting/CountingSort/CountingSortConfig';

const CountingSortSimulation = ({
  currentStep,
  colors,
  maxBarHeight,
  barWidth,
  barSpacing,
  showValues,
  showIndices,
  config,
  state
}) => {
  const theme = useTheme();

  // Extract visualization data from current step
  const visualizationData = currentStep?.visualizationData || {};
  const mainArray = visualizationData.mainArray || {};
  const countArray = visualizationData.countArray || [];
  const outputArray = visualizationData.outputArray || [];
  const maxValue = visualizationData.maxValue;

  const values = mainArray.values || [];
  const currentIndex = mainArray.currentIndex;
  const currentElement = mainArray.currentElement;
  const sortedIndices = mainArray.sortedIndices || [];
  const targetIndex = visualizationData.targetIndex;

  // Calculate positions and dimensions
  const totalWidth = values.length > 0 ? values.length * (barWidth + barSpacing) - barSpacing : 1;
  const startX = -totalWidth / 2;

  // Get colors for different states
  const algorithmColors = getAlgorithmColors(theme);
  const getBarColor = (index, value) => {
    if (sortedIndices.includes(index)) return colors.sorted || algorithmColors.sorted;
    if (index === currentIndex) return colors.current || algorithmColors.comparing;
    if (index === targetIndex) return colors.placing || algorithmColors.swapping;
    return colors.default || algorithmColors.default;
  };

  // Main array bars
  const mainArrayBars = useMemo(() => {
    return values.map((value, index) => {
      if (value === null || value === undefined) return null;

      const x = startX + index * (barWidth + barSpacing);
      const maxVal = Math.max(...values.filter(v => v !== null && v !== undefined && !isNaN(v)));
      const height = maxVal > 0 ? (value / maxVal) * maxBarHeight : 0.1;
      const color = getBarColor(index, value);

      return (
        <group key={`main-${index}`} position={[x, height / 2, 0]}>
          {/* Bar */}
          <mesh>
            <boxGeometry args={[barWidth * 0.8, height, barWidth * 0.8]} />
            <meshStandardMaterial
              color={color}
              roughness={CONFIG.mainArray.bars.material.roughness}
              metalness={CONFIG.mainArray.bars.material.metalness}
            />
          </mesh>

          {/* Value label */}
          {showValues && (
            <Text
              position={[0, height / 2 + 0.3, 0]}
              fontSize={0.4}
              color={theme.palette.text.primary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {value}
            </Text>
          )}

          {/* Index label */}
          {showIndices && (
            <Text
              position={[0, -height / 2 - 0.5, 0]}
              fontSize={0.3}
              color={theme.palette.text.secondary}
              anchorX="center"
              anchorY="middle"
              depthOffset={-1}
            >
              {index}
            </Text>
          )}
        </group>
      );
    }).filter(Boolean);
  }, [values, currentIndex, sortedIndices, targetIndex, barWidth, barSpacing, maxBarHeight, showValues, showIndices, colors, theme]);

  // Count array visualization
  const countArrayBars = useMemo(() => {
    if (countArray.length === 0) return null;

    const countBarWidth = CONFIG.countArray.bars.width;
    const countBarSpacing = CONFIG.countArray.bars.spacing;
    const countMaxHeight = CONFIG.countArray.bars.maxHeight;
    const countTotalWidth = countArray.length * (countBarWidth + countBarSpacing) - countBarSpacing;
    const countStartX = -countTotalWidth / 2;
    const maxCount = Math.max(...countArray, 1);

    return (
      <group position={CONFIG.countArray.position}>
        {/* Count array platform */}
        <mesh position={[0, -0.1, 0]}>
          <boxGeometry args={[countTotalWidth + 0.5, 0.1, 1.5]} />
          <meshStandardMaterial
            color={colors.countPlatform || CONFIG.colors.palette.countPlatform}
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Count bars */}
        {countArray.map((count, index) => {
          const x = countStartX + index * (countBarWidth + countBarSpacing);
          const height = Math.max((count / maxCount) * countMaxHeight, CONFIG.countArray.bars.minHeight);
          const isHighlighted = currentElement === index;
          const color = isHighlighted
            ? (colors.countHighlight || CONFIG.colors.palette.countHighlight)
            : (colors.countBar || CONFIG.colors.palette.countBar);

          return (
            <group key={`count-${index}`} position={[x, height / 2, 0]}>
              {/* Count bar */}
              <mesh>
                <boxGeometry args={[countBarWidth * 0.9, height, countBarWidth * 0.9]} />
                <meshStandardMaterial
                  color={color}
                  roughness={CONFIG.countArray.bars.material.roughness}
                  metalness={CONFIG.countArray.bars.material.metalness}
                />
              </mesh>

              {/* Count value label */}
              <Text
                position={[0, height / 2 + 0.2, 0]}
                fontSize={0.3}
                color={theme.palette.text.primary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {count}
              </Text>

              {/* Index label */}
              <Text
                position={[0, -height / 2 - 0.3, 0]}
                fontSize={0.25}
                color={theme.palette.text.secondary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {index}
              </Text>
            </group>
          );
        })}

        {/* Count array title */}
        <Text
          position={[0, countMaxHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Count Array
        </Text>
      </group>
    );
  }, [countArray, currentElement, colors, theme]);

  // Output array visualization
  const outputArrayBars = useMemo(() => {
    if (outputArray.length === 0) return null;

    const outputStartX = startX; // Same positioning as main array
    const maxOutputValue = Math.max(...outputArray.filter(v => v !== null && v !== undefined && !isNaN(v)), 1);

    return (
      <group position={CONFIG.outputArray.position}>
        {/* Output array platform */}
        <mesh position={[0, -0.1, 0]}>
          <boxGeometry args={[totalWidth + 0.5, 0.1, 2.0]} />
          <meshStandardMaterial
            color={colors.outputPlatform || CONFIG.colors.palette.outputPlatform}
            roughness={0.8}
            metalness={0.2}
          />
        </mesh>

        {/* Output bars */}
        {outputArray.map((value, index) => {
          if (value === null || value === undefined) return null;

          const x = outputStartX + index * (barWidth + barSpacing);
          const height = maxOutputValue > 0 ? (value / maxOutputValue) * maxBarHeight : 0.1;
          const isTarget = index === targetIndex;
          const color = isTarget
            ? (colors.outputHighlight || CONFIG.colors.palette.outputHighlight)
            : (colors.outputBar || CONFIG.colors.palette.outputBar);

          return (
            <group key={`output-${index}`} position={[x, height / 2, 0]}>
              {/* Output bar */}
              <mesh>
                <boxGeometry args={[barWidth * 0.8, height, barWidth * 0.8]} />
                <meshStandardMaterial
                  color={color}
                  roughness={CONFIG.outputArray.bars.material.roughness}
                  metalness={CONFIG.outputArray.bars.material.metalness}
                />
              </mesh>

              {/* Value label */}
              <Text
                position={[0, height / 2 + 0.3, 0]}
                fontSize={0.4}
                color={theme.palette.text.primary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {value}
              </Text>

              {/* Index label */}
              <Text
                position={[0, -height / 2 - 0.5, 0]}
                fontSize={0.3}
                color={theme.palette.text.secondary}
                anchorX="center"
                anchorY="middle"
                depthOffset={-1}
              >
                {index}
              </Text>
            </group>
          );
        }).filter(Boolean)}

        {/* Output array title */}
        <Text
          position={[0, maxBarHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Output Array
        </Text>
      </group>
    );
  }, [outputArray, targetIndex, barWidth, barSpacing, maxBarHeight, totalWidth, startX, colors, theme]);

  return (
    <group>
      {/* Main array */}
      <group position={CONFIG.mainArray.bars.baseOffset}>
        {/* Main array platform */}
        <mesh position={[0, -0.1, 0]}>
          <boxGeometry args={[totalWidth + 0.5, 0.1, 2.0]} />
          <meshStandardMaterial
            color={colors.base || CONFIG.colors.palette.base}
            roughness={CONFIG.mainArray.platform.material.roughness}
            metalness={CONFIG.mainArray.platform.material.metalness}
          />
        </mesh>

        {/* Main array bars */}
        {mainArrayBars}

        {/* Main array title */}
        <Text
          position={[0, maxBarHeight + 0.8, 0]}
          fontSize={0.4}
          color={theme.palette.text.primary}
          anchorX="center"
          anchorY="middle"
          depthOffset={-1}
        >
          Input Array
        </Text>
      </group>

      {/* Count array */}
      {countArrayBars}

      {/* Output array */}
      {outputArrayBars}
    </group>
  );
};

export default CountingSortSimulation;
